{"name": "restaurant-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.24", "@react-navigation/stack": "^7.4.5", "expo": "~53.0.20", "expo-asset": "^11.1.7", "expo-font": "^13.3.2", "expo-status-bar": "~2.2.3", "lucide-react-native": "^0.539.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-safe-area-context": "^5.6.0", "react-native-screens": "^4.13.1", "react-native-web": "^0.21.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}