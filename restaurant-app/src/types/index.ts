// Core App Types
export interface Tenant {
  id: string;
  name: string;
  slug: string;
  logo: string;
  theme: ThemeConfig;
  branding: BrandingConfig;
  isActive: boolean;
}

export interface ThemeConfig {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  scheme: 'blackWhite' | 'redWhite' | 'darkGreenWhite' | 'mustardWhite';
}

export interface BrandingConfig {
  logo: string;
  logoAlt: string;
  companyName: string;
  tagline?: string;
  colors: ThemeConfig;
}

// Menu & Product Types
export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  isAvailable: boolean;
  allergens?: string[];
  nutritionalInfo?: NutritionalInfo;
  customizations?: Customization[];
  tenantId: string;
}

export interface MenuCategory {
  id: string;
  name: string;
  description?: string;
  image?: string;
  sortOrder: number;
  tenantId: string;
}

export interface NutritionalInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  sugar?: number;
}

export interface Customization {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  required: boolean;
  options: CustomizationOption[];
}

export interface CustomizationOption {
  id: string;
  name: string;
  price: number;
  isDefault?: boolean;
}

// Cart & Order Types
export interface CartItem {
  id: string;
  menuItem: MenuItem;
  quantity: number;
  customizations: SelectedCustomization[];
  totalPrice: number;
}

export interface SelectedCustomization {
  customizationId: string;
  optionIds: string[];
}

export interface Cart {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  tenantId: string;
}

export interface Order {
  id: string;
  items: CartItem[];
  totalPrice: number;
  status: OrderStatus;
  createdAt: Date;
  estimatedDelivery?: Date;
  customer: Customer;
  tenantId: string;
  paymentStatus: PaymentStatus;
}

export type OrderStatus = 
  | 'pending' 
  | 'confirmed' 
  | 'preparing' 
  | 'ready' 
  | 'delivered' 
  | 'cancelled';

export type PaymentStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'refunded';

// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserRole;
  tenantId?: string;
  preferences: UserPreferences;
}

export type UserRole = 'customer' | 'vendor' | 'admin';

export interface Customer extends User {
  addresses: Address[];
  orderHistory: Order[];
}

export interface Vendor extends User {
  tenantId: string;
  permissions: VendorPermission[];
}

export interface Address {
  id: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault: boolean;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationPreferences;
  dietary: DietaryPreferences;
}

export interface NotificationPreferences {
  orderUpdates: boolean;
  promotions: boolean;
  newsletter: boolean;
}

export interface DietaryPreferences {
  allergies: string[];
  dietaryRestrictions: string[];
}

export type VendorPermission = 
  | 'manage_menu' 
  | 'manage_orders' 
  | 'manage_events' 
  | 'manage_discounts' 
  | 'view_analytics';

// Event Types
export interface Event {
  id: string;
  title: string;
  description: string;
  image: string;
  startDate: Date;
  endDate: Date;
  location?: string;
  isActive: boolean;
  tenantId: string;
}

// Navigation Types
export type RootStackParamList = {
  Main: undefined;
  ItemDetail: { itemId: string };
  OrderTracking: { orderId: string };
  VendorDashboard: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Menu: undefined;
  Cart: undefined;
  Profile: undefined;
};

// API Response Types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Search Types
export interface SearchFilters {
  category?: string;
  priceRange?: [number, number];
  allergens?: string[];
  isAvailable?: boolean;
  sortBy?: 'name' | 'price' | 'popularity';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchResult {
  items: MenuItem[];
  totalCount: number;
  filters: SearchFilters;
}
