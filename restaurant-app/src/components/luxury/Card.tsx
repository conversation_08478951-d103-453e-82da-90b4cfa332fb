import React from 'react';
import { 
  View, 
  TouchableOpacity, 
  StyleSheet, 
  ViewStyle,
  Image,
  Text,
  ImageSourcePropType,
} from 'react-native';
import { useTheme } from '@/contexts/AppContext';
import { spacing, borderRadius, shadows, typography } from '@/config/themes';

interface CardProps {
  children?: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  variant?: 'elevated' | 'outlined' | 'filled';
  padding?: keyof typeof spacing;
  image?: string | ImageSourcePropType;
  imageHeight?: number;
  title?: string;
  subtitle?: string;
  footer?: React.ReactNode;
  disabled?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  onPress,
  style,
  variant = 'elevated',
  padding = 'md',
  image,
  imageHeight = 200,
  title,
  subtitle,
  footer,
  disabled = false,
}) => {
  const theme = useTheme();

  const getCardStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      borderRadius: borderRadius.lg,
      overflow: 'hidden',
      backgroundColor: theme.surface,
    };

    const variantStyles: Record<string, ViewStyle> = {
      elevated: {
        ...shadows.lg,
        backgroundColor: theme.surface,
      },
      outlined: {
        borderWidth: 1,
        borderColor: theme.border,
        backgroundColor: theme.background,
      },
      filled: {
        backgroundColor: theme.surface,
      },
    };

    return {
      ...baseStyles,
      ...variantStyles[variant],
      ...(disabled && { opacity: 0.6 }),
    };
  };

  const renderImage = () => {
    if (!image) return null;

    const imageSource = typeof image === 'string' ? { uri: image } : image;

    return (
      <Image
        source={imageSource}
        style={{
          width: '100%',
          height: imageHeight,
          resizeMode: 'cover',
        }}
      />
    );
  };

  const renderHeader = () => {
    if (!title && !subtitle) return null;

    return (
      <View style={{ padding: spacing[padding] }}>
        {title && (
          <Text style={[
            typography.h4,
            { 
              color: theme.text,
              marginBottom: subtitle ? spacing.xs : 0,
            }
          ]}>
            {title}
          </Text>
        )}
        {subtitle && (
          <Text style={[
            typography.body2,
            { color: theme.textSecondary }
          ]}>
            {subtitle}
          </Text>
        )}
      </View>
    );
  };

  const renderContent = () => {
    if (!children) return null;

    return (
      <View style={{ 
        padding: spacing[padding],
        paddingTop: (title || subtitle) ? 0 : spacing[padding],
      }}>
        {children}
      </View>
    );
  };

  const renderFooter = () => {
    if (!footer) return null;

    return (
      <View style={{ 
        padding: spacing[padding],
        paddingTop: 0,
        borderTopWidth: 1,
        borderTopColor: theme.border,
        marginTop: spacing.sm,
      }}>
        {footer}
      </View>
    );
  };

  const cardContent = (
    <View style={[getCardStyles(), style]}>
      {renderImage()}
      {renderHeader()}
      {renderContent()}
      {renderFooter()}
    </View>
  );

  if (onPress && !disabled) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.9}
        style={{ flex: 1 }}
      >
        {cardContent}
      </TouchableOpacity>
    );
  }

  return cardContent;
};

// Specialized Card variants for common use cases

interface MenuItemCardProps {
  item: {
    id: string;
    name: string;
    description: string;
    price: number;
    image: string;
    isAvailable: boolean;
  };
  onPress: () => void;
  style?: ViewStyle;
}

export const MenuItemCard: React.FC<MenuItemCardProps> = ({ 
  item, 
  onPress, 
  style 
}) => {
  const theme = useTheme();

  return (
    <Card
      image={item.image}
      imageHeight={160}
      onPress={onPress}
      style={style}
      disabled={!item.isAvailable}
    >
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <View style={{ flex: 1, marginRight: spacing.md }}>
          <Text style={[
            typography.h4,
            { 
              color: theme.text,
              marginBottom: spacing.xs,
            }
          ]}>
            {item.name}
          </Text>
          <Text style={[
            typography.body2,
            { 
              color: theme.textSecondary,
              lineHeight: 20,
            }
          ]} numberOfLines={2}>
            {item.description}
          </Text>
        </View>
        <View style={{ alignItems: 'flex-end' }}>
          <Text style={[
            typography.h4,
            { 
              color: theme.primary,
              fontWeight: '400',
            }
          ]}>
            ${item.price.toFixed(2)}
          </Text>
          {!item.isAvailable && (
            <Text style={[
              typography.caption,
              { 
                color: theme.textSecondary,
                marginTop: spacing.xs,
              }
            ]}>
              Unavailable
            </Text>
          )}
        </View>
      </View>
    </Card>
  );
};

export default Card;
