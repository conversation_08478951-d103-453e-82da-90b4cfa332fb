import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Tenant, ThemeConfig } from '@/types';
import { themeSchemes, defaultTheme } from '@/config/themes';

// Tenant Context Types
interface TenantState {
  currentTenant: Tenant | null;
  theme: ThemeConfig;
  isLoading: boolean;
  error: string | null;
}

type TenantAction =
  | { type: 'SET_TENANT'; payload: Tenant }
  | { type: 'SET_THEME'; payload: ThemeConfig }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_TENANT' };

interface TenantContextType {
  state: TenantState;
  setTenant: (tenant: Tenant) => void;
  setTheme: (theme: ThemeConfig) => void;
  clearTenant: () => void;
  switchThemeScheme: (scheme: string) => void;
}

// Initial state
const initialState: TenantState = {
  currentTenant: null,
  theme: defaultTheme,
  isLoading: false,
  error: null,
};

// Reducer
const tenantReducer = (state: TenantState, action: TenantAction): TenantState => {
  switch (action.type) {
    case 'SET_TENANT':
      return {
        ...state,
        currentTenant: action.payload,
        theme: action.payload.theme,
        isLoading: false,
        error: null,
      };
    case 'SET_THEME':
      return {
        ...state,
        theme: action.payload,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'CLEAR_TENANT':
      return {
        ...initialState,
        theme: defaultTheme,
      };
    default:
      return state;
  }
};

// Create context
const TenantContext = createContext<TenantContextType | undefined>(undefined);

// Provider component
interface TenantProviderProps {
  children: ReactNode;
  initialTenant?: Tenant;
}

export const TenantProvider: React.FC<TenantProviderProps> = ({ 
  children, 
  initialTenant 
}) => {
  const [state, dispatch] = useReducer(tenantReducer, {
    ...initialState,
    currentTenant: initialTenant || null,
    theme: initialTenant?.theme || defaultTheme,
  });

  // Actions
  const setTenant = (tenant: Tenant) => {
    dispatch({ type: 'SET_TENANT', payload: tenant });
  };

  const setTheme = (theme: ThemeConfig) => {
    dispatch({ type: 'SET_THEME', payload: theme });
  };

  const clearTenant = () => {
    dispatch({ type: 'CLEAR_TENANT' });
  };

  const switchThemeScheme = (scheme: string) => {
    const newTheme = themeSchemes[scheme];
    if (newTheme) {
      setTheme(newTheme);
      
      // Update current tenant's theme if tenant exists
      if (state.currentTenant) {
        const updatedTenant: Tenant = {
          ...state.currentTenant,
          theme: newTheme,
        };
        setTenant(updatedTenant);
      }
    }
  };

  // Load tenant from storage on mount
  useEffect(() => {
    const loadTenantFromStorage = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        // In a real app, this would load from AsyncStorage or API
        // For now, we'll use a mock tenant
        const mockTenant: Tenant = {
          id: 'default-tenant',
          name: 'Luxe Restaurant',
          slug: 'luxe-restaurant',
          logo: 'https://example.com/logo.png',
          theme: defaultTheme,
          branding: {
            logo: 'https://example.com/logo.png',
            logoAlt: 'Luxe Restaurant Logo',
            companyName: 'Luxe Restaurant',
            tagline: 'Exquisite Dining Experience',
            colors: defaultTheme,
          },
          isActive: true,
        };

        if (!initialTenant) {
          setTenant(mockTenant);
        }
      } catch (error) {
        dispatch({ 
          type: 'SET_ERROR', 
          payload: 'Failed to load tenant configuration' 
        });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    if (!initialTenant) {
      loadTenantFromStorage();
    }
  }, [initialTenant]);

  const value: TenantContextType = {
    state,
    setTenant,
    setTheme,
    clearTenant,
    switchThemeScheme,
  };

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
};

// Hook to use tenant context
export const useTenant = (): TenantContextType => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

// Hook to get current theme
export const useTheme = (): ThemeConfig => {
  const { state } = useTenant();
  return state.theme;
};

// Hook to get current tenant
export const useCurrentTenant = (): Tenant | null => {
  const { state } = useTenant();
  return state.currentTenant;
};
