import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Cart, CartItem, MenuItem, SelectedCustomization } from '@/types';
import { useTenant } from './TenantContext';

// Cart Context Types
interface CartState {
  cart: Cart;
  isLoading: boolean;
  error: string | null;
}

type CartAction =
  | { type: 'ADD_ITEM'; payload: { menuItem: MenuItem; quantity: number; customizations: SelectedCustomization[] } }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'UPDATE_QUANTITY'; payload: { itemId: string; quantity: number } }
  | { type: 'CLEAR_CART' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOAD_CART'; payload: Cart };

interface CartContextType {
  state: CartState;
  addItem: (menuItem: MenuItem, quantity: number, customizations?: SelectedCustomization[]) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  getCartTotal: () => number;
  getCartItemCount: () => number;
}

// Helper functions
const calculateItemPrice = (
  menuItem: MenuItem, 
  quantity: number, 
  customizations: SelectedCustomization[] = []
): number => {
  let basePrice = menuItem.price * quantity;
  
  // Add customization costs
  customizations.forEach(customization => {
    const menuCustomization = menuItem.customizations?.find(c => c.id === customization.customizationId);
    if (menuCustomization) {
      customization.optionIds.forEach(optionId => {
        const option = menuCustomization.options.find(o => o.id === optionId);
        if (option) {
          basePrice += option.price * quantity;
        }
      });
    }
  });
  
  return basePrice;
};

const generateCartItemId = (menuItem: MenuItem, customizations: SelectedCustomization[]): string => {
  const customizationString = customizations
    .map(c => `${c.customizationId}:${c.optionIds.sort().join(',')}`)
    .sort()
    .join('|');
  return `${menuItem.id}-${customizationString}`;
};

// Initial state
const createInitialState = (tenantId: string): CartState => ({
  cart: {
    items: [],
    totalItems: 0,
    totalPrice: 0,
    tenantId,
  },
  isLoading: false,
  error: null,
});

// Reducer
const cartReducer = (state: CartState, action: CartAction): CartState => {
  switch (action.type) {
    case 'ADD_ITEM': {
      const { menuItem, quantity, customizations } = action.payload;
      const itemId = generateCartItemId(menuItem, customizations);
      const totalPrice = calculateItemPrice(menuItem, quantity, customizations);
      
      const existingItemIndex = state.cart.items.findIndex(item => item.id === itemId);
      
      let updatedItems: CartItem[];
      
      if (existingItemIndex >= 0) {
        // Update existing item
        updatedItems = state.cart.items.map((item, index) => 
          index === existingItemIndex 
            ? { 
                ...item, 
                quantity: item.quantity + quantity,
                totalPrice: calculateItemPrice(menuItem, item.quantity + quantity, customizations)
              }
            : item
        );
      } else {
        // Add new item
        const newItem: CartItem = {
          id: itemId,
          menuItem,
          quantity,
          customizations,
          totalPrice,
        };
        updatedItems = [...state.cart.items, newItem];
      }
      
      const newTotalItems = updatedItems.reduce((sum, item) => sum + item.quantity, 0);
      const newTotalPrice = updatedItems.reduce((sum, item) => sum + item.totalPrice, 0);
      
      return {
        ...state,
        cart: {
          ...state.cart,
          items: updatedItems,
          totalItems: newTotalItems,
          totalPrice: newTotalPrice,
        },
      };
    }
    
    case 'REMOVE_ITEM': {
      const updatedItems = state.cart.items.filter(item => item.id !== action.payload);
      const newTotalItems = updatedItems.reduce((sum, item) => sum + item.quantity, 0);
      const newTotalPrice = updatedItems.reduce((sum, item) => sum + item.totalPrice, 0);
      
      return {
        ...state,
        cart: {
          ...state.cart,
          items: updatedItems,
          totalItems: newTotalItems,
          totalPrice: newTotalPrice,
        },
      };
    }
    
    case 'UPDATE_QUANTITY': {
      const { itemId, quantity } = action.payload;
      
      if (quantity <= 0) {
        return cartReducer(state, { type: 'REMOVE_ITEM', payload: itemId });
      }
      
      const updatedItems = state.cart.items.map(item => 
        item.id === itemId 
          ? { 
              ...item, 
              quantity,
              totalPrice: calculateItemPrice(item.menuItem, quantity, item.customizations)
            }
          : item
      );
      
      const newTotalItems = updatedItems.reduce((sum, item) => sum + item.quantity, 0);
      const newTotalPrice = updatedItems.reduce((sum, item) => sum + item.totalPrice, 0);
      
      return {
        ...state,
        cart: {
          ...state.cart,
          items: updatedItems,
          totalItems: newTotalItems,
          totalPrice: newTotalPrice,
        },
      };
    }
    
    case 'CLEAR_CART':
      return {
        ...state,
        cart: {
          ...state.cart,
          items: [],
          totalItems: 0,
          totalPrice: 0,
        },
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    
    case 'LOAD_CART':
      return {
        ...state,
        cart: action.payload,
        isLoading: false,
        error: null,
      };
    
    default:
      return state;
  }
};

// Create context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Provider component
interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const { state: tenantState } = useTenant();
  const tenantId = tenantState.currentTenant?.id || 'default';
  
  const [state, dispatch] = useReducer(cartReducer, createInitialState(tenantId));

  // Actions
  const addItem = (
    menuItem: MenuItem, 
    quantity: number, 
    customizations: SelectedCustomization[] = []
  ) => {
    dispatch({ 
      type: 'ADD_ITEM', 
      payload: { menuItem, quantity, customizations } 
    });
  };

  const removeItem = (itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: itemId });
  };

  const updateQuantity = (itemId: string, quantity: number) => {
    dispatch({ type: 'UPDATE_QUANTITY', payload: { itemId, quantity } });
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  const getCartTotal = (): number => {
    return state.cart.totalPrice;
  };

  const getCartItemCount = (): number => {
    return state.cart.totalItems;
  };

  // Load cart from storage on mount and tenant change
  useEffect(() => {
    const loadCartFromStorage = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        // In a real app, this would load from AsyncStorage
        // For now, we'll just clear the cart when tenant changes
        if (state.cart.tenantId !== tenantId) {
          dispatch({ type: 'CLEAR_CART' });
        }
      } catch (error) {
        dispatch({ 
          type: 'SET_ERROR', 
          payload: 'Failed to load cart' 
        });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    loadCartFromStorage();
  }, [tenantId]);

  const value: CartContextType = {
    state,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    getCartTotal,
    getCartItemCount,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

// Hook to use cart context
export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
