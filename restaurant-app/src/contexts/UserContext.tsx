import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, Customer, Vendor, UserRole } from '@/types';

// User Context Types
interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

type UserAction =
  | { type: 'SET_USER'; payload: User }
  | { type: 'CLEAR_USER' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<User['preferences']> };

interface UserContextType {
  state: UserState;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (userData: RegisterData) => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  updatePreferences: (preferences: Partial<User['preferences']>) => void;
  isVendor: () => boolean;
  isCustomer: () => boolean;
  hasPermission: (permission: string) => boolean;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserRole;
  tenantId?: string;
}

// Initial state
const initialState: UserState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Reducer
const userReducer = (state: UserState, action: UserAction): UserState => {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'CLEAR_USER':
      return {
        ...initialState,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'UPDATE_PREFERENCES':
      if (!state.user) return state;
      return {
        ...state,
        user: {
          ...state.user,
          preferences: {
            ...state.user.preferences,
            ...action.payload,
          },
        },
      };
    default:
      return state;
  }
};

// Create context
const UserContext = createContext<UserContextType | undefined>(undefined);

// Provider component
interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(userReducer, initialState);

  // Actions
  const login = async (email: string, password: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      // Mock login - in real app, this would call your authentication API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      // Mock user data
      const mockUser: Customer = {
        id: 'user-1',
        email,
        firstName: 'John',
        lastName: 'Doe',
        phone: '+**********',
        role: 'customer',
        preferences: {
          theme: 'light',
          notifications: {
            orderUpdates: true,
            promotions: true,
            newsletter: false,
          },
          dietary: {
            allergies: [],
            dietaryRestrictions: [],
          },
        },
        addresses: [
          {
            id: 'addr-1',
            street: '123 Main St',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            country: 'USA',
            isDefault: true,
          },
        ],
        orderHistory: [],
      };

      dispatch({ type: 'SET_USER', payload: mockUser });
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: 'Login failed. Please check your credentials.' 
      });
      throw error;
    }
  };

  const logout = () => {
    dispatch({ type: 'CLEAR_USER' });
    // In real app, clear tokens from storage
  };

  const register = async (userData: RegisterData): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      // Mock registration - in real app, this would call your registration API
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newUser: User = {
        id: `user-${Date.now()}`,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
        role: userData.role,
        tenantId: userData.tenantId,
        preferences: {
          theme: 'light',
          notifications: {
            orderUpdates: true,
            promotions: true,
            newsletter: false,
          },
          dietary: {
            allergies: [],
            dietaryRestrictions: [],
          },
        },
      };

      dispatch({ type: 'SET_USER', payload: newUser });
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: 'Registration failed. Please try again.' 
      });
      throw error;
    }
  };

  const updateProfile = async (userData: Partial<User>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      if (!state.user) {
        throw new Error('No user logged in');
      }

      // Mock update - in real app, this would call your update API
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedUser: User = {
        ...state.user,
        ...userData,
      };

      dispatch({ type: 'SET_USER', payload: updatedUser });
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: 'Failed to update profile. Please try again.' 
      });
      throw error;
    }
  };

  const updatePreferences = (preferences: Partial<User['preferences']>) => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
  };

  const isVendor = (): boolean => {
    return state.user?.role === 'vendor';
  };

  const isCustomer = (): boolean => {
    return state.user?.role === 'customer';
  };

  const hasPermission = (permission: string): boolean => {
    if (!state.user || state.user.role !== 'vendor') {
      return false;
    }

    const vendor = state.user as Vendor;
    return vendor.permissions?.includes(permission as any) || false;
  };

  // Load user from storage on mount
  useEffect(() => {
    const loadUserFromStorage = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        // In a real app, this would load from AsyncStorage and validate token
        // For now, we'll just set loading to false
        
      } catch (error) {
        dispatch({ 
          type: 'SET_ERROR', 
          payload: 'Failed to load user session' 
        });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    loadUserFromStorage();
  }, []);

  const value: UserContextType = {
    state,
    login,
    logout,
    register,
    updateProfile,
    updatePreferences,
    isVendor,
    isCustomer,
    hasPermission,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

// Hook to use user context
export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// Hook to get current user
export const useCurrentUser = (): User | null => {
  const { state } = useUser();
  return state.user;
};

// Hook to check authentication status
export const useAuth = () => {
  const { state, login, logout, register } = useUser();
  return {
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
    login,
    logout,
    register,
  };
};
