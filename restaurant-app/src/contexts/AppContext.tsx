import React, { ReactNode } from 'react';
import { TenantProvider } from './TenantContext';
import { UserProvider } from './UserContext';
<parameter name="file_content">import { CartProvider } from './CartContext';
import { Tenant } from '@/types';

// Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends React.Component<
  { children: ReactNode; fallback?: ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: ReactNode; fallback?: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('App Error Boundary caught an error:', error, errorInfo);
    // In a real app, you would log this to your error reporting service
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div style={{ 
          flex: 1, 
          justifyContent: 'center', 
          alignItems: 'center', 
          padding: 20 
        }}>
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened.</p>
          <button 
            onClick={() => this.setState({ hasError: false })}
            style={{
              marginTop: 16,
              padding: '8px 16px',
              backgroundColor: '#007AFF',
              color: 'white',
              border: 'none',
              borderRadius: 8,
              cursor: 'pointer',
            }}
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// App Provider Props
interface AppProviderProps {
  children: ReactNode;
  initialTenant?: Tenant;
  errorFallback?: ReactNode;
}

// Main App Provider that wraps all context providers
export const AppProvider: React.FC<AppProviderProps> = ({ 
  children, 
  initialTenant,
  errorFallback 
}) => {
  return (
    <ErrorBoundary fallback={errorFallback}>
      <TenantProvider initialTenant={initialTenant}>
        <UserProvider>
          <CartProvider>
            {children}
          </CartProvider>
        </UserProvider>
      </TenantProvider>
    </ErrorBoundary>
  );
};

// Export all context hooks for easy importing
export { useTenant, useTheme, useCurrentTenant } from './TenantContext';
export { useUser, useCurrentUser, useAuth } from './UserContext';
export { useCart } from './CartContext';
