import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useTheme, useCurrentTenant, useCart } from '@/contexts/AppContext';
import { spacing, typography } from '@/config/themes';
import Button from '@/components/luxury/Button';
import Card, { MenuItemCard } from '@/components/luxury/Card';

const HomeScreen: React.FC = () => {
  const theme = useTheme();
  const tenant = useCurrentTenant();
  const { getCartItemCount } = useCart();

  // Mock data for featured items
  const featuredItems = [
    {
      id: '1',
      name: 'Truffle Risotto',
      description: 'Creamy arborio rice with black truffle and parmesan',
      price: 28.50,
      image: 'https://images.unsplash.com/photo-1476124369491-e7addf5db371?w=400',
      isAvailable: true,
    },
    {
      id: '2',
      name: '<PERSON><PERSON><PERSON> <PERSON><PERSON>derloin',
      description: 'Premium wagyu beef with seasonal vegetables',
      price: 65.00,
      image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400',
      isAvailable: true,
    },
    {
      id: '3',
      name: 'Lobster Thermidor',
      description: 'Fresh lobster with cognac cream sauce',
      price: 42.00,
      image: 'https://images.unsplash.com/photo-1559737558-2f5a35f4523b?w=400',
      isAvailable: false,
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    header: {
      paddingHorizontal: spacing.lg,
      paddingTop: spacing.lg,
      paddingBottom: spacing.md,
    },
    welcomeText: {
      ...typography.h1,
      color: theme.text,
      marginBottom: spacing.xs,
    },
    subtitleText: {
      ...typography.body1,
      color: theme.textSecondary,
    },
    section: {
      paddingHorizontal: spacing.lg,
      marginBottom: spacing.xl,
    },
    sectionTitle: {
      ...typography.h3,
      color: theme.text,
      marginBottom: spacing.md,
    },
    featuredGrid: {
      gap: spacing.md,
    },
    cartSummary: {
      backgroundColor: theme.surface,
      padding: spacing.lg,
      margin: spacing.lg,
      borderRadius: 12,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    cartText: {
      ...typography.body1,
      color: theme.text,
    },
    cartCount: {
      ...typography.h4,
      color: theme.primary,
    },
  });

  const handleItemPress = (itemId: string) => {
    // Navigate to item detail screen
    console.log('Navigate to item:', itemId);
  };

  const handleViewMenu = () => {
    // Navigate to menu screen
    console.log('Navigate to menu');
  };

  const handleViewCart = () => {
    // Navigate to cart screen
    console.log('Navigate to cart');
  };

  const cartItemCount = getCartItemCount();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar 
        barStyle={theme.scheme === 'blackWhite' ? 'dark-content' : 'light-content'}
        backgroundColor={theme.background}
      />
      
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.welcomeText}>
            Welcome to {tenant?.name || 'Luxe Restaurant'}
          </Text>
          <Text style={styles.subtitleText}>
            {tenant?.branding.tagline || 'Exquisite dining experience'}
          </Text>
        </View>

        {/* Cart Summary */}
        {cartItemCount > 0 && (
          <View style={styles.cartSummary}>
            <View>
              <Text style={styles.cartText}>Items in cart</Text>
              <Text style={styles.cartCount}>{cartItemCount} items</Text>
            </View>
            <Button
              title="View Cart"
              onPress={handleViewCart}
              variant="primary"
              size="small"
            />
          </View>
        )}

        {/* Featured Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Featured Items</Text>
          <View style={styles.featuredGrid}>
            {featuredItems.map((item) => (
              <MenuItemCard
                key={item.id}
                item={item}
                onPress={() => handleItemPress(item.id)}
                style={{ marginBottom: spacing.md }}
              />
            ))}
          </View>
        </View>

        {/* Browse Menu CTA */}
        <View style={styles.section}>
          <Card variant="outlined" padding="lg">
            <Text style={[
              typography.h3,
              { color: theme.text, marginBottom: spacing.sm }
            ]}>
              Explore Our Full Menu
            </Text>
            <Text style={[
              typography.body1,
              { color: theme.textSecondary, marginBottom: spacing.lg }
            ]}>
              Discover our complete selection of carefully crafted dishes
            </Text>
            <Button
              title="Browse Menu"
              onPress={handleViewMenu}
              variant="primary"
              fullWidth
            />
          </Card>
        </View>

        {/* Bottom spacing */}
        <View style={{ height: spacing.xl }} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomeScreen;
