import { ThemeConfig } from '@/types';

// Luxury Design Tokens
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
} as const;

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
} as const;

export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
} as const;

// Typography Scale (Lufga Font Family)
export const typography = {
  h1: {
    fontSize: 32,
    fontWeight: '300' as const, // Lufga Light
    lineHeight: 40,
    letterSpacing: -0.5,
  },
  h2: {
    fontSize: 28,
    fontWeight: '300' as const,
    lineHeight: 36,
    letterSpacing: -0.25,
  },
  h3: {
    fontSize: 24,
    fontWeight: '400' as const, // Lufga Regular
    lineHeight: 32,
    letterSpacing: 0,
  },
  h4: {
    fontSize: 20,
    fontWeight: '400' as const,
    lineHeight: 28,
    letterSpacing: 0,
  },
  body1: {
    fontSize: 16,
    fontWeight: '400' as const,
    lineHeight: 24,
    letterSpacing: 0.15,
  },
  body2: {
    fontSize: 14,
    fontWeight: '400' as const,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  caption: {
    fontSize: 12,
    fontWeight: '400' as const,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
  button: {
    fontSize: 16,
    fontWeight: '400' as const,
    lineHeight: 24,
    letterSpacing: 0.5,
    textTransform: 'uppercase' as const,
  },
} as const;

// Four Monochromatic Theme Schemes
export const themeSchemes: Record<string, ThemeConfig> = {
  blackWhite: {
    primary: '#000000',
    secondary: '#FFFFFF',
    accent: '#333333',
    background: '#FFFFFF',
    surface: '#F8F9FA',
    text: '#000000',
    textSecondary: '#666666',
    border: '#E5E5E5',
    scheme: 'blackWhite',
  },
  redWhite: {
    primary: '#DC2626',
    secondary: '#FFFFFF',
    accent: '#EF4444',
    background: '#FFFFFF',
    surface: '#FEF2F2',
    text: '#1F2937',
    textSecondary: '#6B7280',
    border: '#FCA5A5',
    scheme: 'redWhite',
  },
  darkGreenWhite: {
    primary: '#065F46',
    secondary: '#FFFFFF',
    accent: '#059669',
    background: '#FFFFFF',
    surface: '#F0FDF4',
    text: '#1F2937',
    textSecondary: '#6B7280',
    border: '#86EFAC',
    scheme: 'darkGreenWhite',
  },
  mustardWhite: {
    primary: '#D97706',
    secondary: '#FFFFFF',
    accent: '#F59E0B',
    background: '#FFFFFF',
    surface: '#FFFBEB',
    text: '#1F2937',
    textSecondary: '#6B7280',
    border: '#FCD34D',
    scheme: 'mustardWhite',
  },
} as const;

// Default theme (Black & White)
export const defaultTheme = themeSchemes.blackWhite;

// Theme utilities
export const getThemeByScheme = (scheme: string): ThemeConfig => {
  return themeSchemes[scheme] || defaultTheme;
};

export const createCustomTheme = (
  baseScheme: string,
  overrides: Partial<ThemeConfig>
): ThemeConfig => {
  const baseTheme = getThemeByScheme(baseScheme);
  return {
    ...baseTheme,
    ...overrides,
  };
};

// Luxury color palette for additional UI elements
export const luxuryColors = {
  gold: '#FFD700',
  platinum: '#E5E4E2',
  bronze: '#CD7F32',
  pearl: '#F8F6F0',
  charcoal: '#36454F',
  cream: '#FFFDD0',
  champagne: '#F7E7CE',
  rose: '#FF66CC',
} as const;

// Status colors (consistent across all themes)
export const statusColors = {
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  pending: '#6B7280',
} as const;

// Animation durations
export const animations = {
  fast: 150,
  normal: 300,
  slow: 500,
  verySlow: 1000,
} as const;

// Breakpoints for responsive design
export const breakpoints = {
  mobile: 0,
  tablet: 768,
  desktop: 1024,
  largeDesktop: 1440,
} as const;

// Z-index scale
export const zIndex = {
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
} as const;
