{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/navigation/*": ["navigation/*"], "@/contexts/*": ["contexts/*"], "@/hooks/*": ["hooks/*"], "@/services/*": ["services/*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/config/*": ["config/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"]}